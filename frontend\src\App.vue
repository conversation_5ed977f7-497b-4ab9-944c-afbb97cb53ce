<template>
  <div id="app" class="min-h-screen">
    <!-- Simple layout focused on Reality 2.0 functionality -->
    <RouterView />

    <!-- Global notification container -->
    <NotificationContainer />
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import NotificationContainer from '@/components/common/NotificationContainer.vue'

// Simple setup focused on Reality 2.0 functionality
console.log('Reality 2.0 - Czech Real Estate Search Application')
</script>

<style>
/* Simple global styles for Reality 2.0 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
}

#app {
  width: 100%;
  height: 100vh;
}
</style>
