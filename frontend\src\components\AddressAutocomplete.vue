<template>
  <div class="address-autocomplete" ref="autocompleteRef">
    <!-- Input Field -->
    <div class="input-container">
      <input
        ref="inputRef"
        v-model="searchQuery"
        type="text"
        :placeholder="placeholder"
        :disabled="disabled"
        class="address-input"
        :class="{
          'input-loading': isLoading,
          'input-error': error,
          'input-disabled': disabled
        }"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @keydown="handleKeydown"
        autocomplete="off"
      />
      
      <!-- Loading Spinner -->
      <div v-if="isLoading" class="loading-spinner">
        <div class="spinner"></div>
      </div>
      
      <!-- Clear Button -->
      <button
        v-if="searchQuery && !disabled"
        @click="clearInput"
        class="clear-button"
        type="button"
        aria-label="Clear search"
      >
        ×
      </button>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- Suggestions Dropdown -->
    <div
      v-if="showSuggestions && (suggestions.length > 0 || isLoading || shouldShowEmptyState)"
      class="suggestions-dropdown"
      :class="{ 'dropdown-visible': showSuggestions }"
      @mousedown.prevent
    >
      <!-- Loading state -->
      <div v-if="isLoading" class="suggestion-item loading-state">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner"></div>
          </div>
          <span class="loading-text">Vyhledávám města...</span>
        </div>
      </div>

      <!-- Suggestions list -->
      <ul v-else-if="suggestions.length > 0" class="suggestions-list" role="listbox">
        <li
          v-for="(suggestion, index) in suggestions"
          :key="`${suggestion.name}-${suggestion.region}`"
          class="suggestion-item"
          :class="{
            'suggestion-highlighted': index === highlightedIndex,
            'suggestion-selected': selectedCity?.name === suggestion.name
          }"
          role="option"
          :aria-selected="index === highlightedIndex"
          @mousedown="selectSuggestion(suggestion)"
          @mouseenter="highlightedIndex = index"
        >
          <div class="suggestion-content">
            <div class="suggestion-name">{{ suggestion.name }}</div>
            <div class="suggestion-region">{{ suggestion.region }}</div>
            <div class="suggestion-coordinates">
              {{ suggestion.coordinates.lat.toFixed(4) }}, {{ suggestion.coordinates.lng.toFixed(4) }}
            </div>
          </div>
        </li>
      </ul>

      <!-- No results state -->
      <div v-else-if="shouldShowEmptyState" class="suggestion-item empty-state">
        <div class="empty-content">
          <span class="empty-icon">🔍</span>
          <div class="empty-text">
            <div class="empty-title">Žádná města nenalezena</div>
            <div class="empty-message">Zkuste jiný název nebo zkontrolujte překlepy</div>
          </div>
        </div>
      </div>

      <!-- Minimum characters hint -->
      <div v-else-if="searchQuery.length > 0 && searchQuery.length < minChars" class="suggestion-item hint-state">
        <div class="hint-content">
          <span class="hint-icon">💡</span>
          <span class="hint-text">Zadejte alespoň {{ minChars }} znaky pro vyhledávání</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { debounce } from 'lodash-es'
import { realityApi } from '@/services/realityApi'
import type { CitySearchResult } from '@/types/reality'

// Props
interface Props {
  modelValue?: string
  placeholder?: string
  disabled?: boolean
  debounceMs?: number
  minChars?: number
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Zadejte název města...',
  disabled: false,
  debounceMs: 300,
  minChars: 2
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'citySelected', city: CitySearchResult): void
  (e: 'error', error: string): void
  (e: 'clear'): void
}

const emit = defineEmits<Emits>()

// Refs
const autocompleteRef = ref<HTMLElement>()
const inputRef = ref<HTMLInputElement>()

// Reactive state
const searchQuery = ref(props.modelValue)
const suggestions = ref<CitySearchResult[]>([])
const selectedCity = ref<CitySearchResult | null>(null)
const isLoading = ref(false)
const error = ref<string | null>(null)
const showSuggestions = ref(false)
const highlightedIndex = ref(-1)

// Computed
const canSearch = computed(() => {
  return searchQuery.value.length >= props.minChars && !props.disabled
})

const shouldShowEmptyState = computed(() => {
  return !isLoading.value &&
         suggestions.value.length === 0 &&
         searchQuery.value.length >= props.minChars &&
         !error.value
})

// Debounced search function
const debouncedSearch = debounce(async (query: string) => {
  if (!canSearch.value) {
    suggestions.value = []
    showSuggestions.value = false
    return
  }

  isLoading.value = true
  error.value = null

  try {
    const results = await realityApi.cities.searchCities(query)
    suggestions.value = results
    showSuggestions.value = true
    highlightedIndex.value = -1
  } catch (err: any) {
    error.value = err.message || 'Chyba při vyhledávání měst'
    suggestions.value = []
    showSuggestions.value = false
    emit('error', error.value)
  } finally {
    isLoading.value = false
  }
}, props.debounceMs)

// Event handlers
const handleInput = () => {
  emit('update:modelValue', searchQuery.value)
  
  if (selectedCity.value) {
    selectedCity.value = null
  }
  
  if (searchQuery.value.length === 0) {
    suggestions.value = []
    showSuggestions.value = false
    error.value = null
    return
  }
  
  debouncedSearch(searchQuery.value)
}

const handleFocus = () => {
  if (suggestions.value.length > 0) {
    showSuggestions.value = true
  }
}

const handleBlur = () => {
  // Delay hiding suggestions to allow for click events
  setTimeout(() => {
    showSuggestions.value = false
    highlightedIndex.value = -1
  }, 200)
}

const handleKeydown = (event: KeyboardEvent) => {
  if (!showSuggestions.value || suggestions.value.length === 0) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(
        highlightedIndex.value + 1,
        suggestions.value.length - 1
      )
      break
    
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1)
      break
    
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectSuggestion(suggestions.value[highlightedIndex.value])
      }
      break
    
    case 'Escape':
      showSuggestions.value = false
      highlightedIndex.value = -1
      inputRef.value?.blur()
      break
  }
}

const selectSuggestion = (suggestion: CitySearchResult) => {
  selectedCity.value = suggestion
  searchQuery.value = suggestion.name
  suggestions.value = []
  showSuggestions.value = false
  highlightedIndex.value = -1

  emit('update:modelValue', suggestion.name)
  emit('citySelected', suggestion)

  // Focus back to input for better UX
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const clearInput = () => {
  searchQuery.value = ''
  selectedCity.value = null
  suggestions.value = []
  showSuggestions.value = false
  error.value = null
  highlightedIndex.value = -1

  emit('update:modelValue', '')
  emit('clear')

  inputRef.value?.focus()
}

const retrySearch = () => {
  if (searchQuery.value.length >= props.minChars) {
    error.value = null
    debouncedSearch(searchQuery.value)
  }
}

// Click outside handler
const handleClickOutside = (event: Event) => {
  if (autocompleteRef.value && !autocompleteRef.value.contains(event.target as Node)) {
    showSuggestions.value = false
    highlightedIndex.value = -1
  }
}

// Watch for external model value changes
watch(() => props.modelValue, (newValue) => {
  if (newValue !== searchQuery.value) {
    searchQuery.value = newValue
  }
})

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  debouncedSearch.cancel()
})

// Expose selected city for parent components
defineExpose({
  selectedCity: computed(() => selectedCity.value),
  clearInput,
  focus: () => inputRef.value?.focus()
})
</script>

<style scoped>
.address-autocomplete {
  position: relative;
  width: 100%;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.address-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 80px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.5;
  background-color: #ffffff;
  transition: all 0.2s ease;
  outline: none;
}

.address-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.address-input.input-loading {
  padding-right: 100px;
}

.address-input.input-error {
  border-color: #ef4444;
}

.address-input.input-disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

.loading-spinner {
  position: absolute;
  right: 50px;
  display: flex;
  align-items: center;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.clear-button {
  position: absolute;
  right: 16px;
  width: 24px;
  height: 24px;
  border: none;
  background: #6b7280;
  color: white;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1;
  transition: background-color 0.2s ease;
}

.clear-button:hover {
  background: #374151;
}

.error-message {
  margin-top: 4px;
  padding: 8px 12px;
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 6px;
  color: #dc2626;
  font-size: 14px;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  margin-top: 4px;
  background: white;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
}

.suggestions-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.suggestion-item {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.2s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-highlighted {
  background-color: #f8fafc;
}

.suggestion-selected {
  background-color: #eff6ff;
  border-left: 3px solid #3b82f6;
}

.suggestion-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.suggestion-name {
  font-weight: 600;
  color: #1f2937;
  font-size: 16px;
}

.suggestion-region {
  color: #6b7280;
  font-size: 14px;
}

.suggestion-coordinates {
  color: #9ca3af;
  font-size: 12px;
  font-family: monospace;
}

.no-results {
  padding: 16px;
  text-align: center;
  color: #6b7280;
  font-style: italic;
}

/* Enhanced states */
.loading-state,
.empty-state,
.hint-state {
  padding: 16px;
  border-bottom: none !important;
}

.loading-content,
.empty-content,
.hint-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-text {
  color: #6b7280;
  font-size: 14px;
}

.empty-icon,
.hint-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.empty-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.empty-title {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.empty-message {
  color: #6b7280;
  font-size: 12px;
}

.hint-text {
  color: #6b7280;
  font-size: 14px;
}

/* Responsive design */
@media (max-width: 768px) {
  .address-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }
  
  .suggestions-dropdown {
    max-height: 250px;
  }
}
</style>
