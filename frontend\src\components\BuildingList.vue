<template>
  <div class="building-list">
    <!-- Header with building count -->
    <div class="list-header">
      <h3 class="list-title">
        🏢 Nale<PERSON><PERSON> budovy
        <span v-if="buildings.length > 0" class="building-count">
          ({{ buildings.length }})
        </span>
      </h3>
      
      <!-- Loading indicator -->
      <div v-if="loading" class="loading-indicator">
        <div class="spinner"></div>
        <span>Načítám budovy...</span>
      </div>
    </div>

    <!-- Building Type Summary -->
    <div v-if="buildings.length > 0" class="building-summary">
      <div
        v-for="(count, type) in buildingTypesSummary"
        :key="type"
        class="type-summary"
      >
        <span class="type-icon">{{ getTypeIcon(type) }}</span>
        <span class="type-name">{{ getTypeName(type) }}</span>
        <span class="type-count">{{ count }}</span>
      </div>
    </div>

    <!-- Buildings Grid -->
    <div v-if="buildings.length > 0" class="buildings-grid">
      <div
        v-for="building in buildings"
        :key="building.id"
        class="building-card"
      >
        <!-- Building Header -->
        <div class="building-header">
          <div class="building-id">
            <span class="id-label">ID:</span>
            <span class="id-value">{{ building.id }}</span>
          </div>
          <div class="building-type-badge" :class="`type-${building.property_type}`">
            {{ getTypeIcon(building.property_type) }} {{ getTypeName(building.property_type) }}
          </div>
        </div>

        <!-- Building Address -->
        <div class="building-address">
          <span class="address-icon">📍</span>
          <span class="address-text">{{ building.address }}</span>
        </div>

        <!-- Coordinates Section -->
        <div class="coordinates-section">
          <!-- WGS84 Coordinates -->
          <div class="coordinate-group">
            <div class="coordinate-label">
              <span class="coord-icon">🌍</span>
              <span class="coord-title">WGS84 Souřadnice</span>
            </div>
            <div class="coordinate-values">
              <div class="coord-item">
                <span class="coord-name">Lat:</span>
                <span class="coord-value">{{ building.coordinates.lat.toFixed(6) }}</span>
              </div>
              <div class="coord-item">
                <span class="coord-name">Lng:</span>
                <span class="coord-value">{{ building.coordinates.lng.toFixed(6) }}</span>
              </div>
            </div>
          </div>

          <!-- S-JTSK Coordinates -->
          <div class="coordinate-group">
            <div class="coordinate-label">
              <span class="coord-icon">📐</span>
              <span class="coord-title">S-JTSK Souřadnice</span>
            </div>
            <div class="coordinate-values">
              <div class="coord-item">
                <span class="coord-name">X:</span>
                <span class="coord-value">{{ building.sjtsk_coordinates.x.toFixed(0) }}</span>
              </div>
              <div class="coord-item">
                <span class="coord-name">Y:</span>
                <span class="coord-value">{{ building.sjtsk_coordinates.y.toFixed(0) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- CUZK Link -->
        <div v-if="showCadastralLinks" class="building-actions">
          <a
            :href="building.cadastral_link"
            target="_blank"
            rel="noopener noreferrer"
            class="cuzk-link"
            @click="trackCadastralLinkClick(building)"
          >
            <span class="link-icon">🔗</span>
            <span class="link-text">Zobrazit v ČÚZK</span>
            <span class="external-icon">↗</span>
          </a>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!loading" class="empty-state">
      <div class="empty-icon">🏢</div>
      <div class="empty-title">Žádné budovy nenalezeny</div>
      <div class="empty-description">
        Zkuste vybrat jiné město nebo upravit parametry vyhledávání.
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Building } from '@/types/reality'

// Props
interface Props {
  buildings: Building[]
  loading?: boolean
  showCadastralLinks?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showCadastralLinks: true
})

// Emits
interface Emits {
  (e: 'buildingSelected', building: Building): void
  (e: 'cadastralLinkClicked', building: Building): void
}

const emit = defineEmits<Emits>()

// Computed properties
const buildingTypesSummary = computed(() => {
  const summary: Record<string, number> = {}
  props.buildings.forEach(building => {
    summary[building.property_type] = (summary[building.property_type] || 0) + 1
  })
  return summary
})

// Helper functions
const getTypeIcon = (type: string): string => {
  const icons: Record<string, string> = {
    residential: '🏠',
    commercial: '🏢',
    industrial: '🏭',
    other: '🏗️'
  }
  return icons[type] || '🏗️'
}

const getTypeName = (type: string): string => {
  const names: Record<string, string> = {
    residential: 'Obytný',
    commercial: 'Komerční',
    industrial: 'Průmyslový',
    other: 'Ostatní'
  }
  return names[type] || 'Ostatní'
}

const trackCadastralLinkClick = (building: Building) => {
  emit('cadastralLinkClicked', building)
  console.log('ČÚZK link clicked for building:', building.id)
}
</script>

<style scoped>
.building-list {
  width: 100%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.list-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.building-count {
  color: #6b7280;
  font-weight: 400;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e1e5e9;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.building-summary {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.type-summary {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.type-icon {
  font-size: 1.25rem;
}

.type-name {
  font-weight: 500;
  color: #374151;
}

.type-count {
  background: #3b82f6;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.buildings-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
}

.building-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 1.5rem;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.building-card:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.building-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.building-id {
  font-family: monospace;
  font-size: 0.875rem;
}

.id-label {
  color: #9ca3af;
}

.id-value {
  color: #374151;
  font-weight: 500;
}

.building-type-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.type-residential {
  background: #dcfce7;
  color: #166534;
}

.type-commercial {
  background: #dbeafe;
  color: #1e40af;
}

.type-industrial {
  background: #fef3c7;
  color: #92400e;
}

.type-other {
  background: #f3f4f6;
  color: #374151;
}

.building-address {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 8px;
}

.address-icon {
  font-size: 1.125rem;
}

.address-text {
  font-weight: 500;
  color: #1f2937;
  flex: 1;
}

.coordinates-section {
  display: grid;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.coordinate-group {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 1rem;
}

.coordinate-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.coord-icon {
  font-size: 1rem;
}

.coord-title {
  font-weight: 600;
  color: #374151;
  font-size: 0.875rem;
}

.coordinate-values {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.5rem;
}

.coord-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: #f9fafb;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
}

.coord-name {
  color: #6b7280;
  font-weight: 500;
}

.coord-value {
  color: #1f2937;
  font-weight: 600;
}

.building-actions {
  border-top: 1px solid #e5e7eb;
  padding-top: 1rem;
}

.cuzk-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #3b82f6;
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  width: 100%;
  justify-content: center;
}

.cuzk-link:hover {
  background: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.link-icon {
  font-size: 1rem;
}

.link-text {
  flex: 1;
  text-align: center;
}

.external-icon {
  font-size: 0.875rem;
  opacity: 0.8;
}

.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.empty-description {
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Responsive design */
@media (max-width: 768px) {
  .buildings-grid {
    grid-template-columns: 1fr;
  }
  
  .building-header {
    flex-direction: column;
    gap: 0.5rem;
    align-items: flex-start;
  }
  
  .coordinate-values {
    grid-template-columns: 1fr;
  }
  
  .building-summary {
    justify-content: center;
  }
}
</style>
