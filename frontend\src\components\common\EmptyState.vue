<template>
  <div :class="containerClasses" class="text-center py-12">
    <!-- Icon or illustration -->
    <div class="mx-auto mb-4">
      <component
        v-if="icon"
        :is="icon"
        :class="iconClasses"
        class="mx-auto"
        aria-hidden="true"
      />
      <div
        v-else-if="illustration"
        :class="illustrationClasses"
        class="mx-auto flex items-center justify-center rounded-full"
      >
        <span class="text-4xl">{{ illustration }}</span>
      </div>
      <div
        v-else
        :class="defaultIconClasses"
        class="mx-auto flex items-center justify-center rounded-full"
      >
        <ExclamationTriangleIcon class="h-8 w-8" aria-hidden="true" />
      </div>
    </div>

    <!-- Title -->
    <h3 :class="titleClasses" class="font-semibold mb-2">
      {{ title }}
    </h3>

    <!-- Description -->
    <p
      v-if="description"
      :class="descriptionClasses"
      class="mb-6 max-w-sm mx-auto"
    >
      {{ description }}
    </p>

    <!-- Actions -->
    <div
      v-if="actions && actions.length > 0"
      class="flex flex-col sm:flex-row gap-3 justify-center items-center"
    >
      <button
        v-for="action in actions"
        :key="action.label"
        @click="action.handler"
        :disabled="action.disabled"
        :class="getActionClasses(action)"
        class="inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200"
      >
        <component
          v-if="action.icon"
          :is="action.icon"
          class="h-4 w-4 mr-2"
          aria-hidden="true"
        />
        {{ action.label }}
      </button>
    </div>

    <!-- Additional content slot -->
    <div v-if="$slots.default" class="mt-6">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { ExclamationTriangleIcon } from '@heroicons/vue/24/outline'

// Types
interface EmptyStateAction {
  label: string
  handler: () => void
  variant?: 'primary' | 'secondary' | 'outline'
  icon?: any
  disabled?: boolean
}

// Props
interface Props {
  title: string
  description?: string
  icon?: any
  illustration?: string
  variant?: 'default' | 'search' | 'error' | 'info'
  size?: 'sm' | 'md' | 'lg'
  actions?: EmptyStateAction[]
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  size: 'md'
})

// Computed
const containerClasses = computed(() => {
  const sizes = {
    sm: 'py-8',
    md: 'py-12',
    lg: 'py-16'
  }
  return sizes[props.size]
})

const iconClasses = computed(() => {
  const sizes = {
    sm: 'h-12 w-12',
    md: 'h-16 w-16',
    lg: 'h-20 w-20'
  }
  
  const variants = {
    default: 'text-gray-400',
    search: 'text-blue-400',
    error: 'text-red-400',
    info: 'text-indigo-400'
  }
  
  return `${sizes[props.size]} ${variants[props.variant]}`
})

const illustrationClasses = computed(() => {
  const sizes = {
    sm: 'h-16 w-16',
    md: 'h-20 w-20',
    lg: 'h-24 w-24'
  }
  
  const variants = {
    default: 'bg-gray-100 text-gray-400',
    search: 'bg-blue-100 text-blue-400',
    error: 'bg-red-100 text-red-400',
    info: 'bg-indigo-100 text-indigo-400'
  }
  
  return `${sizes[props.size]} ${variants[props.variant]}`
})

const defaultIconClasses = computed(() => {
  const sizes = {
    sm: 'h-16 w-16',
    md: 'h-20 w-20',
    lg: 'h-24 w-24'
  }
  
  const variants = {
    default: 'bg-gray-100 text-gray-400',
    search: 'bg-blue-100 text-blue-400',
    error: 'bg-red-100 text-red-400',
    info: 'bg-indigo-100 text-indigo-400'
  }
  
  return `${sizes[props.size]} ${variants[props.variant]}`
})

const titleClasses = computed(() => {
  const sizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl'
  }
  
  return `${sizes[props.size]} text-gray-900 dark:text-white`
})

const descriptionClasses = computed(() => {
  const sizes = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }
  
  return `${sizes[props.size]} text-gray-600 dark:text-gray-400`
})

// Methods
const getActionClasses = (action: EmptyStateAction) => {
  const baseClasses = 'disabled:opacity-50 disabled:cursor-not-allowed'
  
  switch (action.variant) {
    case 'primary':
      return `${baseClasses} border-transparent text-white bg-indigo-600 hover:bg-indigo-700 focus:ring-indigo-500 disabled:hover:bg-indigo-600`
    case 'outline':
      return `${baseClasses} border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-indigo-500 dark:border-gray-600 dark:text-gray-300 dark:bg-gray-800 dark:hover:bg-gray-700`
    case 'secondary':
    default:
      return `${baseClasses} border-transparent text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:ring-indigo-500 dark:text-indigo-400 dark:bg-indigo-900 dark:hover:bg-indigo-800`
  }
}
</script>
