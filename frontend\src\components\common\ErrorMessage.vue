<template>
  <Transition
    enter-active-class="transition-all duration-300 ease-out"
    enter-from-class="opacity-0 transform scale-95"
    enter-to-class="opacity-100 transform scale-100"
    leave-active-class="transition-all duration-200 ease-in"
    leave-from-class="opacity-100 transform scale-100"
    leave-to-class="opacity-0 transform scale-95"
  >
    <div
      v-if="visible"
      :class="containerClasses"
      class="rounded-md p-4 mb-4"
    >
      <div class="flex">
        <!-- Icon -->
        <div class="flex-shrink-0">
          <component
            :is="iconComponent"
            :class="iconClasses"
            class="h-5 w-5"
            aria-hidden="true"
          />
        </div>
        
        <!-- Content -->
        <div class="ml-3 flex-1">
          <!-- Title -->
          <h3
            v-if="title"
            :class="titleClasses"
            class="text-sm font-medium"
          >
            {{ title }}
          </h3>
          
          <!-- Message -->
          <div
            :class="messageClasses"
            class="text-sm"
            :class="{ 'mt-1': title }"
          >
            <p v-if="typeof message === 'string'">{{ message }}</p>
            <div v-else-if="Array.isArray(message)">
              <ul class="list-disc list-inside space-y-1">
                <li v-for="(msg, index) in message" :key="index">
                  {{ msg }}
                </li>
              </ul>
            </div>
          </div>
          
          <!-- Actions -->
          <div
            v-if="actions && actions.length > 0"
            class="mt-4 flex space-x-3"
          >
            <button
              v-for="action in actions"
              :key="action.label"
              @click="action.handler"
              :disabled="action.disabled"
              :class="getActionClasses(action)"
              class="text-sm font-medium rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200"
            >
              {{ action.label }}
            </button>
          </div>
          
          <!-- Details (collapsible) -->
          <div v-if="details" class="mt-3">
            <button
              @click="showDetails = !showDetails"
              class="text-sm font-medium text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none"
            >
              {{ showDetails ? 'Skrýt detaily' : 'Zobrazit detaily' }}
              <ChevronDownIcon
                :class="{ 'transform rotate-180': showDetails }"
                class="inline h-4 w-4 ml-1 transition-transform duration-200"
              />
            </button>
            
            <Transition
              enter-active-class="transition-all duration-300 ease-out"
              enter-from-class="opacity-0 max-h-0"
              enter-to-class="opacity-100 max-h-96"
              leave-active-class="transition-all duration-200 ease-in"
              leave-from-class="opacity-100 max-h-96"
              leave-to-class="opacity-0 max-h-0"
            >
              <div
                v-if="showDetails"
                class="mt-2 p-3 bg-gray-50 dark:bg-gray-800 rounded-md overflow-hidden"
              >
                <pre class="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap">{{ details }}</pre>
              </div>
            </Transition>
          </div>
        </div>
        
        <!-- Close button -->
        <div v-if="dismissible" class="ml-auto pl-3">
          <div class="-mx-1.5 -my-1.5">
            <button
              @click="dismiss"
              :class="closeButtonClasses"
              class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200"
            >
              <span class="sr-only">Zavřít</span>
              <XMarkIcon class="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon,
  ChevronDownIcon
} from '@heroicons/vue/24/outline'

// Types
interface ErrorAction {
  label: string
  handler: () => void
  variant?: 'primary' | 'secondary'
  disabled?: boolean
}

// Props
interface Props {
  message: string | string[]
  title?: string
  variant?: 'error' | 'warning' | 'info'
  dismissible?: boolean
  visible?: boolean
  actions?: ErrorAction[]
  details?: string
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'error',
  dismissible: true,
  visible: true
})

// Emits
interface Emits {
  (e: 'dismiss'): void
}

const emit = defineEmits<Emits>()

// State
const showDetails = ref(false)

// Computed
const iconComponent = computed(() => {
  const icons = {
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon
  }
  return icons[props.variant]
})

const containerClasses = computed(() => {
  const variants = {
    error: 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800',
    warning: 'bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800',
    info: 'bg-blue-50 border border-blue-200 dark:bg-blue-900/20 dark:border-blue-800'
  }
  return variants[props.variant]
})

const iconClasses = computed(() => {
  const variants = {
    error: 'text-red-400',
    warning: 'text-yellow-400',
    info: 'text-blue-400'
  }
  return variants[props.variant]
})

const titleClasses = computed(() => {
  const variants = {
    error: 'text-red-800 dark:text-red-200',
    warning: 'text-yellow-800 dark:text-yellow-200',
    info: 'text-blue-800 dark:text-blue-200'
  }
  return variants[props.variant]
})

const messageClasses = computed(() => {
  const variants = {
    error: 'text-red-700 dark:text-red-300',
    warning: 'text-yellow-700 dark:text-yellow-300',
    info: 'text-blue-700 dark:text-blue-300'
  }
  return variants[props.variant]
})

const closeButtonClasses = computed(() => {
  const variants = {
    error: 'text-red-400 hover:bg-red-100 focus:ring-red-600 dark:hover:bg-red-800',
    warning: 'text-yellow-400 hover:bg-yellow-100 focus:ring-yellow-600 dark:hover:bg-yellow-800',
    info: 'text-blue-400 hover:bg-blue-100 focus:ring-blue-600 dark:hover:bg-blue-800'
  }
  return variants[props.variant]
})

// Methods
const dismiss = () => {
  emit('dismiss')
}

const getActionClasses = (action: ErrorAction) => {
  const baseClasses = 'disabled:opacity-50 disabled:cursor-not-allowed'
  
  if (action.variant === 'primary') {
    const variants = {
      error: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
      warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
      info: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500'
    }
    return `${baseClasses} ${variants[props.variant]}`
  }
  
  // Secondary variant
  const variants = {
    error: 'bg-red-100 text-red-800 hover:bg-red-200 focus:ring-red-500 dark:bg-red-800 dark:text-red-200 dark:hover:bg-red-700',
    warning: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 focus:ring-yellow-500 dark:bg-yellow-800 dark:text-yellow-200 dark:hover:bg-yellow-700',
    info: 'bg-blue-100 text-blue-800 hover:bg-blue-200 focus:ring-blue-500 dark:bg-blue-800 dark:text-blue-200 dark:hover:bg-blue-700'
  }
  return `${baseClasses} ${variants[props.variant]}`
}
</script>
