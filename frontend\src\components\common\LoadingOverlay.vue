<template>
  <Transition
    enter-active-class="transition-opacity duration-300"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition-opacity duration-300"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      :class="overlayClasses"
      class="absolute inset-0 flex items-center justify-center"
    >
      <!-- Background overlay -->
      <div
        :class="backgroundClasses"
        class="absolute inset-0"
      ></div>

      <!-- Loading content -->
      <div class="relative z-10 flex flex-col items-center space-y-4">
        <!-- Spinner -->
        <div class="relative">
          <LoadingSpinner
            :size="spinnerSize"
            :color="spinnerColor"
          />

          <!-- Progress ring (if progress is provided) -->
          <div
            v-if="typeof progress === 'number'"
            class="absolute inset-0 flex items-center justify-center"
          >
            <svg
              :class="progressRingClasses"
              class="transform -rotate-90"
              :width="progressRingSize"
              :height="progressRingSize"
            >
              <circle
                cx="50%"
                cy="50%"
                :r="progressRadius"
                fill="transparent"
                :stroke-width="progressStrokeWidth"
                class="stroke-gray-200 dark:stroke-gray-700"
              />
              <circle
                cx="50%"
                cy="50%"
                :r="progressRadius"
                fill="transparent"
                :stroke-width="progressStrokeWidth"
                :stroke-dasharray="progressCircumference"
                :stroke-dashoffset="progressOffset"
                class="stroke-current transition-all duration-300 ease-out"
                :class="progressStrokeClasses"
              />
            </svg>
            <div class="absolute inset-0 flex items-center justify-center">
              <span
                :class="progressTextClasses"
                class="text-sm font-medium"
              >
                {{ Math.round(progress) }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Loading message -->
        <div
          v-if="message"
          class="text-center max-w-xs"
        >
          <p
            :class="messageClasses"
            class="text-sm font-medium"
          >
            {{ message }}
          </p>
          <p
            v-if="description"
            :class="descriptionClasses"
            class="text-xs mt-1"
          >
            {{ description }}
          </p>
        </div>

        <!-- Cancel button -->
        <button
          v-if="cancellable"
          @click="$emit('cancel')"
          class="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors duration-200"
        >
          Zrušit
        </button>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import LoadingSpinner from './LoadingSpinner.vue'

// Props
interface Props {
  visible?: boolean
  message?: string
  description?: string
  progress?: number
  variant?: 'overlay' | 'inline' | 'modal'
  size?: 'sm' | 'md' | 'lg'
  cancellable?: boolean
  blur?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  variant: 'overlay',
  size: 'md',
  cancellable: false,
  blur: true
})

// Emits
interface Emits {
  (e: 'cancel'): void
}

defineEmits<Emits>()

// Computed
const overlayClasses = computed(() => {
  const base = 'z-50'

  if (props.variant === 'modal') {
    return `${base} fixed inset-0`
  }

  return base
})

const backgroundClasses = computed(() => {
  const base = props.blur ? 'backdrop-blur-sm' : ''

  if (props.variant === 'overlay') {
    return `${base} bg-white/80 dark:bg-gray-900/80`
  } else if (props.variant === 'modal') {
    return `${base} bg-black/50`
  }

  return `${base} bg-gray-50/90 dark:bg-gray-800/90`
})

const spinnerSize = computed(() => {
  const sizes = {
    sm: 'md',
    md: 'lg',
    lg: 'xl'
  }
  return sizes[props.size] as 'md' | 'lg' | 'xl'
})

const spinnerColor = computed(() => {
  return props.variant === 'modal' ? 'white' : 'primary'
})

const messageClasses = computed(() => {
  return props.variant === 'modal'
    ? 'text-white'
    : 'text-gray-900 dark:text-white'
})

const descriptionClasses = computed(() => {
  return props.variant === 'modal'
    ? 'text-gray-300'
    : 'text-gray-600 dark:text-gray-400'
})

// Progress ring calculations
const progressRingSize = computed(() => {
  const sizes = {
    sm: 48,
    md: 64,
    lg: 80
  }
  return sizes[props.size]
})

const progressRadius = computed(() => progressRingSize.value / 2 - 8)
const progressStrokeWidth = computed(() => 3)
const progressCircumference = computed(() => 2 * Math.PI * progressRadius.value)

const progressOffset = computed(() => {
  if (typeof props.progress !== 'number') return 0
  return progressCircumference.value - (props.progress / 100) * progressCircumference.value
})

const progressRingClasses = computed(() => {
  return props.variant === 'modal' ? 'text-white' : 'text-primary-600'
})

const progressStrokeClasses = computed(() => {
  return props.variant === 'modal' ? 'stroke-white' : 'stroke-primary-600'
})

const progressTextClasses = computed(() => {
  return props.variant === 'modal' ? 'text-white' : 'text-gray-900 dark:text-white'
})
</script>
