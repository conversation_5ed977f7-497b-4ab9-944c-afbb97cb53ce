<template>
  <Teleport to="body">
    <div
      v-if="notificationStore.hasNotifications"
      :class="containerClasses"
      class="fixed z-50 space-y-3 pointer-events-none"
    >
      <ToastNotification
        v-for="notification in notificationStore.activeNotifications"
        :key="notification.id"
        :notification="notification"
        :show-progress="true"
        class="pointer-events-auto"
        @close="notificationStore.removeNotification(notification.id)"
        @action="handleNotificationAction"
      />
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useNotificationStore } from '@/stores/notifications'
import ToastNotification from './ToastNotification.vue'
import type { ToastAction } from '@/types/app'

// Props
interface Props {
  position?: 'top-right' | 'top-left' | 'bottom-right' | 'bottom-left' | 'top-center' | 'bottom-center'
  maxWidth?: string
}

const props = withDefaults(defineProps<Props>(), {
  position: 'top-right',
  maxWidth: '384px' // max-w-sm equivalent
})

// Store
const notificationStore = useNotificationStore()

// Computed
const containerClasses = computed(() => {
  const positions = {
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-left': 'bottom-4 left-4',
    'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
    'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2'
  }

  return positions[props.position] || positions['top-right']
})

// Methods
const handleNotificationAction = (action: ToastAction) => {
  // Action is already executed in ToastNotification component
  // This is just for additional handling if needed
  console.log('Notification action executed:', action.label)
}
</script>
