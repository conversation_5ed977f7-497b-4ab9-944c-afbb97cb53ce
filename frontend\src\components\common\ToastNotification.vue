<template>
  <Transition
    enter-active-class="transform ease-out duration-300 transition"
    enter-from-class="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2"
    enter-to-class="translate-y-0 opacity-100 sm:translate-x-0"
    leave-active-class="transition ease-in duration-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="visible"
      :class="toastClasses"
      class="max-w-sm w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden"
    >
      <div class="p-4">
        <div class="flex items-start">
          <!-- Icon -->
          <div class="flex-shrink-0">
            <component
              :is="iconComponent"
              :class="iconClasses"
              class="h-6 w-6"
              aria-hidden="true"
            />
          </div>
          
          <!-- Content -->
          <div class="ml-3 w-0 flex-1 pt-0.5">
            <p
              v-if="notification.title"
              class="text-sm font-medium text-gray-900 dark:text-white"
            >
              {{ notification.title }}
            </p>
            <p
              class="text-sm text-gray-500 dark:text-gray-300"
              :class="{ 'mt-1': notification.title }"
            >
              {{ notification.message }}
            </p>
            
            <!-- Actions -->
            <div
              v-if="notification.actions && notification.actions.length > 0"
              class="mt-3 flex space-x-2"
            >
              <button
                v-for="action in notification.actions"
                :key="action.label"
                @click="handleActionClick(action)"
                :class="getActionClasses(action.style)"
                class="text-sm font-medium rounded-md px-3 py-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2"
              >
                {{ action.label }}
              </button>
            </div>
          </div>
          
          <!-- Close button -->
          <div class="ml-4 flex-shrink-0 flex">
            <button
              @click="close"
              class="bg-white dark:bg-gray-800 rounded-md inline-flex text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <span class="sr-only">Zavřít</span>
              <XMarkIcon class="h-5 w-5" aria-hidden="true" />
            </button>
          </div>
        </div>
      </div>
      
      <!-- Progress bar for auto-dismiss -->
      <div
        v-if="!notification.persistent && showProgress"
        class="h-1 bg-gray-200 dark:bg-gray-700"
      >
        <div
          :class="progressBarClasses"
          class="h-full transition-all ease-linear"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import type { Toast, ToastAction } from '@/types/app'

// Props
interface Props {
  notification: Toast
  showProgress?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showProgress: true
})

// Emits
interface Emits {
  (e: 'close'): void
  (e: 'action', action: ToastAction): void
}

const emit = defineEmits<Emits>()

// State
const visible = ref(true)
const progressPercentage = ref(100)
let progressInterval: number | null = null

// Computed
const iconComponent = computed(() => {
  const icons = {
    success: CheckCircleIcon,
    error: ExclamationCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
    system: InformationCircleIcon
  }
  return icons[props.notification.type] || InformationCircleIcon
})

const iconClasses = computed(() => {
  const classes = {
    success: 'text-green-400',
    error: 'text-red-400',
    warning: 'text-yellow-400',
    info: 'text-blue-400',
    system: 'text-gray-400'
  }
  return classes[props.notification.type] || 'text-gray-400'
})

const toastClasses = computed(() => {
  const classes = {
    success: 'border-l-4 border-green-400',
    error: 'border-l-4 border-red-400',
    warning: 'border-l-4 border-yellow-400',
    info: 'border-l-4 border-blue-400',
    system: 'border-l-4 border-gray-400'
  }
  return classes[props.notification.type] || 'border-l-4 border-gray-400'
})

const progressBarClasses = computed(() => {
  const classes = {
    success: 'bg-green-400',
    error: 'bg-red-400',
    warning: 'bg-yellow-400',
    info: 'bg-blue-400',
    system: 'bg-gray-400'
  }
  return classes[props.notification.type] || 'bg-gray-400'
})

// Methods
const close = () => {
  visible.value = false
  setTimeout(() => {
    emit('close')
  }, 100)
}

const handleActionClick = (action: ToastAction) => {
  action.action()
  emit('action', action)
  close()
}

const getActionClasses = (style?: string) => {
  const baseClasses = 'transition-colors duration-200'
  
  switch (style) {
    case 'primary':
      return `${baseClasses} bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500`
    case 'danger':
      return `${baseClasses} bg-red-600 text-white hover:bg-red-700 focus:ring-red-500`
    case 'secondary':
    default:
      return `${baseClasses} bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500 dark:bg-gray-600 dark:text-white dark:hover:bg-gray-500`
  }
}

const startProgressBar = () => {
  if (props.notification.persistent || !props.showProgress) return
  
  const duration = props.notification.duration
  const interval = 50 // Update every 50ms
  const decrement = (interval / duration) * 100
  
  progressInterval = window.setInterval(() => {
    progressPercentage.value -= decrement
    if (progressPercentage.value <= 0) {
      progressPercentage.value = 0
      if (progressInterval) {
        clearInterval(progressInterval)
      }
    }
  }, interval)
}

// Lifecycle
onMounted(() => {
  startProgressBar()
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
})
</script>
