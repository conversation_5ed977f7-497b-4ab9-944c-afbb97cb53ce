/**
 * Test suite for the notification system components and store.
 * Tests the notification store, toast notifications, and error handling.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { setActivePinia, createPinia } from 'pinia'
import { useNotificationStore } from '@/stores/notifications'

describe('Notification System', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })

  describe('Notification Store', () => {
    it('should add notifications correctly', () => {
      const store = useNotificationStore()
      
      const id = store.addNotification('Test message', 'info')
      
      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0].message).toBe('Test message')
      expect(store.notifications[0].type).toBe('info')
      expect(store.notifications[0].id).toBe(id)
    })

    it('should remove notifications correctly', () => {
      const store = useNotificationStore()
      
      const id = store.addNotification('Test message', 'info')
      expect(store.notifications).toHaveLength(1)
      
      store.removeNotification(id)
      expect(store.notifications).toHaveLength(0)
    })

    it('should limit the number of notifications', () => {
      const store = useNotificationStore()
      store.maxNotifications = 3
      
      // Add more notifications than the limit
      for (let i = 0; i < 5; i++) {
        store.addNotification(`Message ${i}`, 'info')
      }
      
      expect(store.notifications).toHaveLength(3)
      // Should keep the most recent notifications
      expect(store.notifications[0].message).toBe('Message 4')
      expect(store.notifications[1].message).toBe('Message 3')
      expect(store.notifications[2].message).toBe('Message 2')
    })

    it('should provide convenience methods for different types', () => {
      const store = useNotificationStore()
      
      const successId = store.success('Success message')
      const errorId = store.error('Error message')
      const warningId = store.warning('Warning message')
      const infoId = store.info('Info message')
      
      expect(store.notifications).toHaveLength(4)
      expect(store.notifications.find(n => n.id === successId)?.type).toBe('success')
      expect(store.notifications.find(n => n.id === errorId)?.type).toBe('error')
      expect(store.notifications.find(n => n.id === warningId)?.type).toBe('warning')
      expect(store.notifications.find(n => n.id === infoId)?.type).toBe('info')
    })

    it('should handle API errors correctly', () => {
      const store = useNotificationStore()
      
      const mockError = {
        response: {
          data: {
            detail: 'API Error Message'
          },
          status: 500
        }
      }
      
      const id = store.handleApiError(mockError, 'Test Context')
      
      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0].message).toBe('Test Context: API Error Message')
      expect(store.notifications[0].type).toBe('error')
      expect(store.notifications[0].title).toBe('Chyba')
    })

    it('should handle loading notifications', () => {
      const store = useNotificationStore()
      
      const loadingId = store.showLoading('Loading data...')
      
      expect(store.notifications).toHaveLength(1)
      expect(store.notifications[0].persistent).toBe(true)
      expect(store.notifications[0].message).toBe('Loading data...')
      
      store.hideLoading(loadingId)
      expect(store.notifications).toHaveLength(0)
    })

    it('should clear all notifications', () => {
      const store = useNotificationStore()
      
      store.addNotification('Message 1', 'info')
      store.addNotification('Message 2', 'success')
      store.addNotification('Message 3', 'error')
      
      expect(store.notifications).toHaveLength(3)
      
      store.clearAllNotifications()
      expect(store.notifications).toHaveLength(0)
    })

    it('should update notifications', () => {
      const store = useNotificationStore()
      
      const id = store.addNotification('Original message', 'info')
      
      store.updateNotification(id, {
        message: 'Updated message',
        type: 'success'
      })
      
      expect(store.notifications[0].message).toBe('Updated message')
      expect(store.notifications[0].type).toBe('success')
    })

    it('should compute active notifications correctly', () => {
      const store = useNotificationStore()
      
      // Add persistent notification
      store.addNotification('Persistent', 'info', { persistent: true })
      
      // Add regular notification
      store.addNotification('Regular', 'info', { persistent: false, duration: 5000 })
      
      expect(store.activeNotifications).toHaveLength(2)
      expect(store.hasNotifications).toBe(true)
    })
  })

  describe('Error Handling', () => {
    it('should handle network errors with retry actions', () => {
      const store = useNotificationStore()
      
      const networkError = {
        code: 'NETWORK_ERROR',
        message: 'Network connection failed'
      }
      
      const id = store.handleApiError(networkError)
      const notification = store.notifications.find(n => n.id === id)
      
      expect(notification?.actions).toHaveLength(1)
      expect(notification?.actions?.[0].label).toBe('Zkusit znovu')
      expect(notification?.actions?.[0].style).toBe('primary')
    })

    it('should handle server errors with retry actions', () => {
      const store = useNotificationStore()
      
      const serverError = {
        response: {
          status: 500,
          data: {
            detail: 'Internal server error'
          }
        }
      }
      
      const id = store.handleApiError(serverError)
      const notification = store.notifications.find(n => n.id === id)
      
      expect(notification?.actions).toHaveLength(1)
      expect(notification?.actions?.[0].label).toBe('Zkusit znovu')
    })

    it('should handle client errors without retry actions', () => {
      const store = useNotificationStore()
      
      const clientError = {
        response: {
          status: 400,
          data: {
            detail: 'Bad request'
          }
        }
      }
      
      const id = store.handleApiError(clientError)
      const notification = store.notifications.find(n => n.id === id)
      
      expect(notification?.actions).toHaveLength(0)
    })
  })
})
