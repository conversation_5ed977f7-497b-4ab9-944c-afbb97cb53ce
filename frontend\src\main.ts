/**
 * Main entry point for Reality 2.0 Vue.js application.
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'

// Styles
import './assets/styles/main.css'

// Create Vue application
const app = createApp(App)

// Create Pinia store
const pinia = createPinia()

// Use plugins
app.use(pinia)
app.use(router)

// Global properties
app.config.globalProperties.$appName = 'Reality 2.0'
app.config.globalProperties.$appVersion = '1.0.0'

// Error handling
app.config.errorHandler = (err, instance, info) => {
  console.error('Global error:', err)
  console.error('Component instance:', instance)
  console.error('Error info:', info)
}

// Mount the application
app.mount('#app')

console.log('🚀 Reality 2.0 started')
