/**
 * Vue Router configuration
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// Import views
const RealityView = () => import('@/views/RealityView.vue')
const DashboardView = () => import('@/views/DashboardView.vue')

// Define routes
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'Reality',
    component: RealityView,
    meta: {
      title: 'Reality 2.0 - Czech Real Estate Search',
      requiresAuth: false,
    },
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: DashboardView,
    meta: {
      title: 'Dashboard',
      requiresAuth: false,
    },
  },
  // Catch-all route for 404
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: RealityView, // Use Reality view as fallback
    meta: {
      title: 'Reality 2.0',
      requiresAuth: false,
    },
  },
]

// Create router
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  },
})

// Navigation guards
router.beforeEach((to, from, next) => {
  // Update document title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - ${import.meta.env.VITE_APP_NAME || 'Reality 2.0'}`
  }
  
  // Add authentication check here if needed
  // if (to.meta?.requiresAuth && !isAuthenticated()) {
  //   next('/login')
  //   return
  // }
  
  next()
})

export default router
