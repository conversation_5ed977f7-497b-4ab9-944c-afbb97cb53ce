/**
 * Reality 2.0 API Service
 * Handles all API calls to the FastAPI backend
 */

import api from './api'
import type {
  CitySearchResult,
  Building,
  CoordinateMesh,
  BuildingSearchParams,
  MeshGenerationParams,
  ApiResponse,
  ApiError
} from '@/types/reality'

// API endpoints
const ENDPOINTS = {
  MAPY_AUTOCOMPLETE: '/api/mapy-autocomplete',
  CITY_SEARCH: '/api/v1/cities/search',
  BUILDING_SEARCH: '/api/v1/buildings',
  BUILDING_BATCH: '/api/v1/buildings/batch',
  FIND_BUILDINGS: '/api/find-buildings',
  MESH_GENERATION: '/api/v1/mesh',
  HEALTH: '/health'
} as const

/**
 * Mapy.cz Autocomplete API
 */
export interface MapyCzSuggestion {
  name: string
  type: string
  lat: number
  lon: number
  bbox: number[] // [min_lon, min_lat, max_lon, max_lat] in WGS84
}

export const mapyCzApi = {
  /**
   * Get autocomplete suggestions from Mapy.cz API
   */
  async autocomplete(query: string): Promise<MapyCzSuggestion[]> {
    try {
      const response = await api.get(ENDPOINTS.MAPY_AUTOCOMPLETE, {
        params: { query }
      })
      return response.data
    } catch (error: any) {
      console.error('Mapy.cz autocomplete error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to get autocomplete suggestions from Mapy.cz')
    }
  }
}

/**
 * City Search API
 */
export const citySearchApi = {
  /**
   * Search for Czech cities using Mapy.cz autocomplete
   */
  async searchCities(query: string): Promise<CitySearchResult[]> {
    try {
      const response = await api.get(ENDPOINTS.CITY_SEARCH, {
        params: { q: query }
      })
      return response.data
    } catch (error: any) {
      console.error('City search error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to search cities')
    }
  }
}

/**
 * Building Search API
 */
export const buildingSearchApi = {
  /**
   * Search for buildings near coordinates using RUIAN API
   */
  async searchBuildings(params: BuildingSearchParams): Promise<Building[]> {
    try {
      const response = await api.get(ENDPOINTS.BUILDING_SEARCH, {
        params: {
          lat: params.lat,
          lng: params.lng,
          radius: params.radius || 0.001
        }
      })
      return response.data
    } catch (error: any) {
      console.error('Building search error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to search buildings')
    }
  },

  /**
   * Batch search for buildings at multiple coordinates
   */
  async batchSearchBuildings(
    coordinates: Array<{ lat: number; lng: number }>,
    radius: number = 0.001
  ): Promise<{ total_buildings: number; searched_coordinates: number; buildings: Building[] }> {
    try {
      const response = await api.post(ENDPOINTS.BUILDING_BATCH, coordinates, {
        params: { radius }
      })
      return response.data
    } catch (error: any) {
      console.error('Batch building search error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to batch search buildings')
    }
  },

  /**
   * Find buildings within a city using the new /api/find-buildings endpoint
   */
  async findBuildings(city: CitySearchResult, radius: number = 0.01): Promise<{
    city: CitySearchResult;
    total_buildings: number;
    buildings: Building[];
    search_radius: number;
    search_duration_ms: number;
  }> {
    try {
      const requestBody = {
        name: city.name,
        region: city.region,
        coordinates: city.coordinates,
        bounding_box: city.bounding_box,
        radius: radius
      }

      const response = await api.post(ENDPOINTS.FIND_BUILDINGS, requestBody)
      return response.data
    } catch (error: any) {
      console.error('Find buildings error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to find buildings')
    }
  }
}

/**
 * Coordinate Mesh API
 */
export const meshApi = {
  /**
   * Generate coordinate mesh within bounding box
   */
  async generateMesh(params: MeshGenerationParams): Promise<CoordinateMesh> {
    try {
      const response = await api.get(ENDPOINTS.MESH_GENERATION, {
        params: {
          north: params.north,
          south: params.south,
          east: params.east,
          west: params.west,
          grid_size: params.grid_size || 0.001
        }
      })
      return response.data
    } catch (error: any) {
      console.error('Mesh generation error:', error)
      throw new Error(error.response?.data?.detail || 'Failed to generate coordinate mesh')
    }
  }
}

/**
 * Health Check API
 */
export const healthApi = {
  /**
   * Check API health status
   */
  async checkHealth(): Promise<any> {
    try {
      const response = await api.get(ENDPOINTS.HEALTH)
      return response.data
    } catch (error: any) {
      console.error('Health check error:', error)
      throw new Error('API health check failed')
    }
  }
}

/**
 * Combined Reality API service
 */
export const realityApi = {
  mapyCz: mapyCzApi,
  cities: citySearchApi,
  buildings: buildingSearchApi,
  mesh: meshApi,
  health: healthApi,

  /**
   * Find buildings in a selected city using the new /api/find-buildings endpoint
   */
  async findBuildingsInCity(
    city: CitySearchResult,
    radius: number = 0.01
  ): Promise<{
    city: CitySearchResult;
    total_buildings: number;
    buildings: Building[];
    search_radius: number;
    search_duration_ms: number;
  }> {
    try {
      return await this.buildings.findBuildings(city, radius)
    } catch (error: any) {
      console.error('Find buildings in city error:', error)
      throw error
    }
  },

  /**
   * Complete workflow: Search city -> Generate mesh -> Find buildings
   */
  async completeSearch(
    cityName: string,
    gridSize: number = 0.01
  ): Promise<{
    city: CitySearchResult
    mesh: CoordinateMesh
    buildings: Building[]
  }> {
    try {
      // 1. Search for city
      const cities = await this.cities.searchCities(cityName)
      if (cities.length === 0) {
        throw new Error(`No cities found for "${cityName}"`)
      }
      const city = cities[0]

      // 2. Generate coordinate mesh
      const mesh = await this.mesh.generateMesh({
        north: city.bounding_box.north,
        south: city.bounding_box.south,
        east: city.bounding_box.east,
        west: city.bounding_box.west,
        grid_size: gridSize
      })

      // 3. Search for buildings using sample points
      const buildings = await this.buildings.batchSearchBuildings(
        mesh.sample_points,
        gridSize / 2
      )

      return {
        city,
        mesh,
        buildings: buildings.buildings
      }
    } catch (error: any) {
      console.error('Complete search error:', error)
      throw error
    }
  }
}

/**
 * Error handling utilities
 */
export const apiErrorHandler = {
  /**
   * Extract error message from API response
   */
  getErrorMessage(error: any): string {
    if (error.response?.data?.detail) {
      return error.response.data.detail
    }
    if (error.response?.data?.message) {
      return error.response.data.message
    }
    if (error.message) {
      return error.message
    }
    return 'An unexpected error occurred'
  },

  /**
   * Check if error is network related
   */
  isNetworkError(error: any): boolean {
    return !error.response && error.request
  },

  /**
   * Check if error is server error (5xx)
   */
  isServerError(error: any): boolean {
    return error.response?.status >= 500
  },

  /**
   * Check if error is client error (4xx)
   */
  isClientError(error: any): boolean {
    return error.response?.status >= 400 && error.response?.status < 500
  }
}

export default realityApi
