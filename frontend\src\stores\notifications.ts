/**
 * Notification store for managing toast notifications and user feedback.
 * Provides centralized notification management with auto-dismiss and actions.
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Toast, NotificationType, ToastAction } from '@/types/app'

export const useNotificationStore = defineStore('notifications', () => {
  // State
  const notifications = ref<Toast[]>([])
  const maxNotifications = ref(5)
  const defaultDuration = ref(5000) // 5 seconds

  // Computed
  const activeNotifications = computed(() => 
    notifications.value.filter(n => !n.persistent || n.duration > 0)
  )

  const hasNotifications = computed(() => notifications.value.length > 0)

  // Actions
  const generateId = (): string => {
    return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  const addNotification = (
    message: string,
    type: NotificationType = 'info',
    options: Partial<Omit<Toast, 'id' | 'message' | 'type'>> = {}
  ): string => {
    const id = generateId()
    
    const notification: Toast = {
      id,
      message,
      type,
      title: options.title,
      duration: options.duration ?? defaultDuration.value,
      persistent: options.persistent ?? false,
      actions: options.actions ?? []
    }

    notifications.value.unshift(notification)

    // Limit the number of notifications
    if (notifications.value.length > maxNotifications.value) {
      notifications.value = notifications.value.slice(0, maxNotifications.value)
    }

    // Auto-dismiss non-persistent notifications
    if (!notification.persistent && notification.duration > 0) {
      setTimeout(() => {
        removeNotification(id)
      }, notification.duration)
    }

    return id
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const updateNotification = (id: string, updates: Partial<Toast>) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      Object.assign(notification, updates)
    }
  }

  // Convenience methods for different notification types
  const success = (message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) => {
    return addNotification(message, 'success', options)
  }

  const error = (message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) => {
    return addNotification(message, 'error', { 
      duration: 8000, // Longer duration for errors
      persistent: false,
      ...options 
    })
  }

  const warning = (message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) => {
    return addNotification(message, 'warning', options)
  }

  const info = (message: string, options?: Partial<Omit<Toast, 'id' | 'message' | 'type'>>) => {
    return addNotification(message, 'info', options)
  }

  // API error handling helper
  const handleApiError = (error: any, context?: string) => {
    let message = 'Došlo k neočekávané chybě'
    
    if (error?.response?.data?.detail) {
      message = error.response.data.detail
    } else if (error?.message) {
      message = error.message
    }

    if (context) {
      message = `${context}: ${message}`
    }

    // Add retry action for network errors
    const actions: ToastAction[] = []
    if (error?.code === 'NETWORK_ERROR' || error?.response?.status >= 500) {
      actions.push({
        label: 'Zkusit znovu',
        action: () => {
          // This would be handled by the calling component
          console.log('Retry action triggered')
        },
        style: 'primary'
      })
    }

    return addNotification(message, 'error', {
      duration: 10000,
      actions,
      title: 'Chyba'
    })
  }

  // Loading notification helper
  const showLoading = (message: string, title?: string): string => {
    return addNotification(message, 'info', {
      persistent: true,
      title: title || 'Načítání...'
    })
  }

  const hideLoading = (id: string) => {
    removeNotification(id)
  }

  return {
    // State
    notifications,
    maxNotifications,
    defaultDuration,

    // Computed
    activeNotifications,
    hasNotifications,

    // Actions
    addNotification,
    removeNotification,
    clearAllNotifications,
    updateNotification,

    // Convenience methods
    success,
    error,
    warning,
    info,
    handleApiError,
    showLoading,
    hideLoading
  }
})
