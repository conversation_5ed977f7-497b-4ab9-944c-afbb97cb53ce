/**
 * Reality 2.0 Pinia Store
 * Manages application state for Czech real estate search
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { realityApi } from '@/services/realityApi'
import type {
  CitySearchResult,
  Building,
  CoordinateMesh,
  SearchState,
  BuildingSearchState,
  MeshState,
  SearchFilters,
  SearchOptions
} from '@/types/reality'

export const useRealityStore = defineStore('reality', () => {
  // State
  const searchState = ref<SearchState>({
    selectedCity: null,
    searchQuery: '',
    suggestions: [],
    isLoading: false,
    error: null
  })

  const buildingState = ref<BuildingSearchState>({
    buildings: [],
    isLoading: false,
    error: null,
    searchParams: null
  })

  const meshState = ref<MeshState>({
    mesh: null,
    isGenerating: false,
    error: null
  })

  const filters = ref<SearchFilters>({
    propertyTypes: ['residential', 'commercial', 'industrial'],
    radiusKm: 1,
    minResults: 1,
    maxResults: 100
  })

  const options = ref<SearchOptions>({
    includeAddress: true,
    includeCadastralLinks: true,
    generateMesh: true,
    meshGridSize: 0.01
  })

  // Statistics
  const statistics = ref({
    totalSearches: 0,
    totalBuildings: 0,
    averageResultsPerSearch: 0,
    searchDuration: 0,
    lastSearchTime: null as Date | null
  })

  // Getters
  const hasSelectedCity = computed(() => searchState.value.selectedCity !== null)
  const hasBuildings = computed(() => buildingState.value.buildings.length > 0)
  const hasMesh = computed(() => meshState.value.mesh !== null)
  const isAnyLoading = computed(() => 
    searchState.value.isLoading || 
    buildingState.value.isLoading || 
    meshState.value.isGenerating
  )

  const filteredBuildings = computed(() => {
    if (!hasBuildings.value) return []
    
    return buildingState.value.buildings.filter(building => 
      filters.value.propertyTypes.includes(building.property_type)
    )
  })

  const buildingsByType = computed(() => {
    const grouped: Record<string, Building[]> = {}
    filteredBuildings.value.forEach(building => {
      if (!grouped[building.property_type]) {
        grouped[building.property_type] = []
      }
      grouped[building.property_type].push(building)
    })
    return grouped
  })

  // Actions
  const setSelectedCity = (city: CitySearchResult | null) => {
    searchState.value.selectedCity = city
    searchState.value.error = null

    // Clear previous results when city changes
    if (city) {
      buildingState.value.buildings = []
      meshState.value.mesh = null
    }
  }

  const setSearchQuery = (query: string) => {
    searchState.value.searchQuery = query
  }

  const setSuggestions = (suggestions: CitySearchResult[]) => {
    searchState.value.suggestions = suggestions
  }

  const setSearchLoading = (loading: boolean) => {
    searchState.value.isLoading = loading
  }

  const setSearchError = (error: string | null) => {
    searchState.value.error = error
  }

  const clearSearch = () => {
    searchState.value.selectedCity = null
    searchState.value.searchQuery = ''
    searchState.value.suggestions = []
    searchState.value.error = null
    buildingState.value.buildings = []
    buildingState.value.error = null
    meshState.value.mesh = null
    meshState.value.error = null
  }

  // Error clearing methods
  const clearSearchError = () => {
    searchState.value.error = null
  }

  const clearBuildingError = () => {
    buildingState.value.error = null
  }

  const clearMeshError = () => {
    meshState.value.error = null
  }

  // Building actions
  const searchBuildings = async (lat: number, lng: number, radius: number = 0.001) => {
    buildingState.value.isLoading = true
    buildingState.value.error = null
    buildingState.value.searchParams = { lat, lng, radius }

    const startTime = Date.now()

    try {
      const buildings = await realityApi.buildings.searchBuildings({ lat, lng, radius })
      buildingState.value.buildings = buildings
      
      // Update statistics
      statistics.value.totalSearches++
      statistics.value.totalBuildings += buildings.length
      statistics.value.searchDuration = Date.now() - startTime
      statistics.value.lastSearchTime = new Date()
      statistics.value.averageResultsPerSearch = 
        statistics.value.totalBuildings / statistics.value.totalSearches

    } catch (error: any) {
      buildingState.value.error = error.message
      console.error('Building search failed:', error)
    } finally {
      buildingState.value.isLoading = false
    }
  }

  const batchSearchBuildings = async (coordinates: Array<{ lat: number; lng: number }>, radius: number = 0.001) => {
    buildingState.value.isLoading = true
    buildingState.value.error = null

    const startTime = Date.now()

    try {
      const result = await realityApi.buildings.batchSearchBuildings(coordinates, radius)
      buildingState.value.buildings = result.buildings

      // Update statistics
      statistics.value.totalSearches++
      statistics.value.totalBuildings += result.buildings.length
      statistics.value.searchDuration = Date.now() - startTime
      statistics.value.lastSearchTime = new Date()
      statistics.value.averageResultsPerSearch =
        statistics.value.totalBuildings / statistics.value.totalSearches

    } catch (error: any) {
      buildingState.value.error = error.message
      console.error('Batch building search failed:', error)
    } finally {
      buildingState.value.isLoading = false
    }
  }

  // Find buildings in selected city using new endpoint
  const findBuildingsInCity = async (city: CitySearchResult, radius: number = 0.01) => {
    buildingState.value.isLoading = true
    buildingState.value.error = null

    const startTime = Date.now()

    try {
      const result = await realityApi.findBuildingsInCity(city, radius)
      buildingState.value.buildings = result.buildings

      // Update statistics
      statistics.value.totalSearches++
      statistics.value.totalBuildings += result.total_buildings
      statistics.value.searchDuration = result.search_duration_ms
      statistics.value.lastSearchTime = new Date()
      statistics.value.averageResultsPerSearch =
        statistics.value.totalBuildings / statistics.value.totalSearches

      console.log(`Found ${result.total_buildings} buildings in ${city.name} (${result.search_duration_ms}ms)`)

    } catch (error: any) {
      buildingState.value.error = error.message
      console.error('Find buildings in city failed:', error)
    } finally {
      buildingState.value.isLoading = false
    }
  }

  // Mesh actions
  const generateMesh = async (city: CitySearchResult, gridSize: number = 0.01) => {
    meshState.value.isGenerating = true
    meshState.value.error = null

    try {
      const mesh = await realityApi.mesh.generateMesh({
        north: city.bounding_box.north,
        south: city.bounding_box.south,
        east: city.bounding_box.east,
        west: city.bounding_box.west,
        grid_size: gridSize
      })
      meshState.value.mesh = mesh
    } catch (error: any) {
      meshState.value.error = error.message
      console.error('Mesh generation failed:', error)
    } finally {
      meshState.value.isGenerating = false
    }
  }

  // Complete search workflow
  const performCompleteSearch = async (cityName: string, gridSize: number = 0.01) => {
    try {
      setSearchLoading(true)
      
      const result = await realityApi.completeSearch(cityName, gridSize)
      
      setSelectedCity(result.city)
      meshState.value.mesh = result.mesh
      buildingState.value.buildings = result.buildings
      
      // Update statistics
      statistics.value.totalSearches++
      statistics.value.totalBuildings += result.buildings.length
      statistics.value.lastSearchTime = new Date()
      statistics.value.averageResultsPerSearch = 
        statistics.value.totalBuildings / statistics.value.totalSearches

    } catch (error: any) {
      setSearchError(error.message)
      console.error('Complete search failed:', error)
    } finally {
      setSearchLoading(false)
    }
  }

  // Filter actions
  const updateFilters = (newFilters: Partial<SearchFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const updateOptions = (newOptions: Partial<SearchOptions>) => {
    options.value = { ...options.value, ...newOptions }
  }

  const resetFilters = () => {
    filters.value = {
      propertyTypes: ['residential', 'commercial', 'industrial'],
      radiusKm: 1,
      minResults: 1,
      maxResults: 100
    }
  }

  // Export building data
  const exportBuildings = (format: 'json' | 'csv' = 'json') => {
    const buildings = filteredBuildings.value
    
    if (format === 'json') {
      const dataStr = JSON.stringify(buildings, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `buildings_${Date.now()}.json`
      link.click()
      URL.revokeObjectURL(url)
    } else if (format === 'csv') {
      const headers = ['ID', 'Address', 'Property Type', 'Latitude', 'Longitude', 'S-JTSK X', 'S-JTSK Y', 'Cadastral Link']
      const csvContent = [
        headers.join(','),
        ...buildings.map(building => [
          building.id,
          `"${building.address}"`,
          building.property_type,
          building.coordinates.lat,
          building.coordinates.lng,
          building.sjtsk_coordinates.x,
          building.sjtsk_coordinates.y,
          `"${building.cadastral_link}"`
        ].join(','))
      ].join('\n')
      
      const dataBlob = new Blob([csvContent], { type: 'text/csv' })
      const url = URL.createObjectURL(dataBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = `buildings_${Date.now()}.csv`
      link.click()
      URL.revokeObjectURL(url)
    }
  }

  return {
    // State
    searchState,
    buildingState,
    meshState,
    filters,
    options,
    statistics,
    
    // Getters
    hasSelectedCity,
    hasBuildings,
    hasMesh,
    isAnyLoading,
    filteredBuildings,
    buildingsByType,
    
    // Actions
    setSelectedCity,
    setSearchQuery,
    setSuggestions,
    setSearchLoading,
    setSearchError,
    clearSearch,
    clearSearchError,
    clearBuildingError,
    clearMeshError,
    searchBuildings,
    batchSearchBuildings,
    findBuildingsInCity,
    generateMesh,
    performCompleteSearch,
    updateFilters,
    updateOptions,
    resetFilters,
    exportBuildings
  }
})

export type RealityStore = ReturnType<typeof useRealityStore>
