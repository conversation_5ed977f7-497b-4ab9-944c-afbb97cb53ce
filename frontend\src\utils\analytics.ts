/**
 * Analytics utilities for Reality 2.0
 * Handles Google Analytics, Mixpanel, and other analytics integrations
 */

// Google Analytics types
declare global {
  interface Window {
    gtag: (...args: any[]) => void
    dataLayer: any[]
  }
}

/**
 * Google Analytics gtag function
 */
export const gtag = (...args: any[]) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag(...args)
  } else {
    console.warn('Google Analytics not initialized')
  }
}

/**
 * Initialize Google Analytics
 */
export const initializeGoogleAnalytics = (measurementId: string) => {
  if (typeof window === 'undefined') return

  // Create gtag script
  const script = document.createElement('script')
  script.async = true
  script.src = `https://www.googletagmanager.com/gtag/js?id=${measurementId}`
  document.head.appendChild(script)

  // Initialize dataLayer and gtag
  window.dataLayer = window.dataLayer || []
  window.gtag = function() {
    window.dataLayer.push(arguments)
  }

  // Configure gtag
  gtag('js', new Date())
  gtag('config', measurementId, {
    page_title: document.title,
    page_location: window.location.href,
  })
}

/**
 * Track page views
 */
export const trackPageView = (path: string, title?: string) => {
  gtag('config', 'GA_MEASUREMENT_ID', {
    page_path: path,
    page_title: title || document.title,
  })
}

/**
 * Track custom events
 */
export const trackEvent = (
  eventName: string,
  parameters: Record<string, any> = {}
) => {
  gtag('event', eventName, parameters)
}

/**
 * Track building search events
 */
export const trackBuildingSearch = (cityName: string, buildingCount: number) => {
  trackEvent('building_search', {
    city_name: cityName,
    building_count: buildingCount,
    search_type: 'find_buildings',
  })
}

/**
 * Track city selection events
 */
export const trackCitySelection = (cityName: string, region: string) => {
  trackEvent('city_selection', {
    city_name: cityName,
    region: region,
  })
}

/**
 * Track mesh generation events
 */
export const trackMeshGeneration = (cityName: string, gridSize: number, pointCount: number) => {
  trackEvent('mesh_generation', {
    city_name: cityName,
    grid_size: gridSize,
    point_count: pointCount,
  })
}

/**
 * Track data export events
 */
export const trackDataExport = (format: string, recordCount: number) => {
  trackEvent('data_export', {
    export_format: format,
    record_count: recordCount,
  })
}

/**
 * Track errors
 */
export const trackError = (errorType: string, errorMessage: string, context?: string) => {
  trackEvent('error', {
    error_type: errorType,
    error_message: errorMessage,
    error_context: context,
  })
}

/**
 * Track performance metrics
 */
export const trackPerformance = (metricName: string, value: number, unit: string = 'ms') => {
  trackEvent('performance', {
    metric_name: metricName,
    metric_value: value,
    metric_unit: unit,
  })
}

/**
 * Simple analytics wrapper for development
 */
export const analytics = {
  initialize: initializeGoogleAnalytics,
  pageView: trackPageView,
  event: trackEvent,
  buildingSearch: trackBuildingSearch,
  citySelection: trackCitySelection,
  meshGeneration: trackMeshGeneration,
  dataExport: trackDataExport,
  error: trackError,
  performance: trackPerformance,
}

export default analytics
