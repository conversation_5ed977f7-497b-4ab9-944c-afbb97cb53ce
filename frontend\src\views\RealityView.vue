<template>
  <div class="reality-view">
    <!-- Compact Header -->
    <header class="app-header">
      <div class="container">
        <h1 class="app-title">
          🏠 Reality 2.0 - Vyhledávání nemovitostí
        </h1>
      </div>
    </header>

    <!-- Main Content -->
    <main class="main-content">
      <div class="container">
        <!-- Search Section -->
        <section class="search-section">
          <div class="search-card">
            <h2 class="section-title">Vyhledat město</h2>
            <p class="section-description">
              Začněte zadáním názvu města pro vyhledání budov a nemovitostí
            </p>
            
            <!-- Address Autocomplete Component -->
            <div class="search-input-container">
              <AddressAutocomplete
                v-model="searchQuery"
                placeholder="Zadejte název města (např. Praha, Brno, Ostrava...)"
                :debounce-ms="300"
                :min-chars="2"
                @city-selected="handleCitySelected"
                @error="handleSearchError"
                @clear="handleSearchClear"
              />
            </div>

            <!-- Search Actions -->
            <div v-if="store.hasSelectedCity" class="search-actions">
              <button
                @click="startBuildingSearch"
                :disabled="store.isAnyLoading"
                class="btn btn-primary"
              >
                <span v-if="store.buildingState.isLoading">
                  🔄 Vyhledávám budovy...
                </span>
                <span v-else>
                  🏢 Vyhledat budovy
                </span>
              </button>

              <button
                @click="generateCoordinateMesh"
                :disabled="store.isAnyLoading"
                class="btn btn-secondary"
              >
                <span v-if="store.meshState.isGenerating">
                  📐 Generuji síť...
                </span>
                <span v-else>
                  📐 Generovat síť souřadnic
                </span>
              </button>
            </div>
          </div>
        </section>

        <!-- Selected City Info -->
        <section v-if="store.hasSelectedCity" class="city-info-section">
          <div class="info-card">
            <h3 class="card-title">Vybrané město</h3>
            <div class="city-details">
              <div class="city-name">
                📍 {{ store.searchState.selectedCity?.name }}
              </div>
              <div class="city-region">
                🗺️ {{ store.searchState.selectedCity?.region }}
              </div>
              <div class="city-coordinates">
                🌍 {{ store.searchState.selectedCity?.coordinates.lat.toFixed(4) }}, 
                {{ store.searchState.selectedCity?.coordinates.lng.toFixed(4) }}
              </div>
              <div class="bounding-box">
                📦 Hranice: 
                S: {{ store.searchState.selectedCity?.bounding_box.north.toFixed(4) }}, 
                J: {{ store.searchState.selectedCity?.bounding_box.south.toFixed(4) }}, 
                V: {{ store.searchState.selectedCity?.bounding_box.east.toFixed(4) }}, 
                Z: {{ store.searchState.selectedCity?.bounding_box.west.toFixed(4) }}
              </div>
            </div>
          </div>
        </section>

        <!-- Coordinate Mesh Info -->
        <section v-if="store.hasMesh" class="mesh-info-section">
          <div class="info-card">
            <h3 class="card-title">Síť souřadnic</h3>
            <div class="mesh-details">
              <div class="mesh-stat">
                📊 Celkem bodů: {{ store.meshState.mesh?.total_points.toLocaleString() }}
              </div>
              <div class="mesh-stat">
                📏 Velikost sítě: {{ store.meshState.mesh?.grid_size }}°
              </div>
              <div class="mesh-stat">
                🎯 Ukázkové body: {{ store.meshState.mesh?.sample_points.length }}
              </div>
            </div>
          </div>
        </section>

        <!-- Buildings Results -->
        <section v-if="store.hasBuildings || store.buildingState.isLoading" class="buildings-section">
          <div class="results-card">
            <div class="results-header">
              <div class="results-info">
                <div v-if="store.buildingState.searchParams" class="search-info">
                  📍 Vyhledáno v okolí:
                  {{ store.buildingState.searchParams.lat.toFixed(4) }},
                  {{ store.buildingState.searchParams.lng.toFixed(4) }}
                  (radius: {{ (store.buildingState.searchParams.radius || 0.001) * 111 }}km)
                </div>
                <div v-if="store.statistics.searchDuration" class="search-stats">
                  <span>⏱️ {{ store.statistics.searchDuration }}ms</span>
                  <span>📊 {{ store.statistics.totalSearches }} vyhledávání</span>
                  <span>🏢 ⌀ {{ store.statistics.averageResultsPerSearch.toFixed(1) }} budov</span>
                </div>
              </div>
              <div v-if="store.hasBuildings" class="results-actions">
                <button @click="exportData('json')" class="btn btn-outline">
                  📄 Export JSON
                </button>
                <button @click="exportData('csv')" class="btn btn-outline">
                  📊 Export CSV
                </button>
              </div>
            </div>

            <!-- Building List Component -->
            <BuildingList
              :buildings="store.buildingState.buildings"
              :loading="store.buildingState.isLoading"
              :show-cadastral-links="true"
              @building-selected="handleBuildingSelected"
              @cadastral-link-clicked="handleCadastralLinkClicked"
            />
          </div>
        </section>

        <!-- Error Messages -->
        <section v-if="hasErrors" class="error-section">
          <div class="error-card">
            <h3 class="error-title">⚠️ Chyby</h3>
            <div v-if="store.searchState.error" class="error-message">
              Vyhledávání: {{ store.searchState.error }}
            </div>
            <div v-if="store.buildingState.error" class="error-message">
              Budovy: {{ store.buildingState.error }}
            </div>
            <div v-if="store.meshState.error" class="error-message">
              Síť: {{ store.meshState.error }}
            </div>
          </div>
        </section>

        <!-- Statistics -->
        <section v-if="store.statistics.totalSearches > 0" class="stats-section">
          <div class="stats-card">
            <h3 class="card-title">📊 Statistiky</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ store.statistics.totalSearches }}</div>
                <div class="stat-label">Celkem vyhledávání</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ store.statistics.totalBuildings }}</div>
                <div class="stat-label">Celkem budov</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ store.statistics.averageResultsPerSearch.toFixed(1) }}</div>
                <div class="stat-label">Průměr na vyhledávání</div>
              </div>
              <div class="stat-item">
                <div class="stat-value">{{ store.statistics.searchDuration }}ms</div>
                <div class="stat-label">Doba posledního vyhledávání</div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRealityStore } from '@/stores/realityStore'
import AddressAutocomplete from '@/components/AddressAutocomplete.vue'
import BuildingList from '@/components/BuildingList.vue'
import type { CitySearchResult, Building } from '@/types/reality'

// Store
const store = useRealityStore()

// Reactive state
const searchQuery = ref('')

// Computed
const hasErrors = computed(() => 
  store.searchState.error || 
  store.buildingState.error || 
  store.meshState.error
)

// Event handlers
const handleCitySelected = (city: CitySearchResult) => {
  store.setSelectedCity(city)
}

const handleSearchError = (error: string) => {
  console.error('Search error:', error)
  store.setSearchError(error)
}

const handleSearchClear = () => {
  console.log('Search cleared')
  store.clearSearch()
  searchQuery.value = ''
}

// Actions
const startBuildingSearch = async () => {
  if (!store.searchState.selectedCity) return

  const city = store.searchState.selectedCity
  // Use the new find buildings endpoint that accepts city object
  await store.findBuildingsInCity(city, 0.01) // 1km radius approximately
}

const generateCoordinateMesh = async () => {
  if (!store.searchState.selectedCity) return
  
  await store.generateMesh(store.searchState.selectedCity, 0.01)
}

const exportData = (format: 'json' | 'csv') => {
  store.exportBuildings(format)
}

// Building List event handlers
const handleBuildingSelected = (building: Building) => {
  console.log('Building selected:', building)
  // You can add additional logic here, such as:
  // - Highlighting the building on a map
  // - Showing detailed building information
  // - Tracking analytics
}

const handleCadastralLinkClicked = (building: Building) => {
  console.log('ČÚZK cadastral link clicked for building:', building.id)
  // Track analytics for cadastral link clicks
  // You can add analytics tracking here if needed
}
</script>

<style scoped>
/* Reality 2.0 Styles */
.reality-view {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.app-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: white;
  text-align: center;
  margin-bottom: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.main-content {
  padding: 1rem 0;
}

/* Cards */
.search-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  margin-bottom: 1.5rem;
  border: 2px solid #3b82f6;
}

.info-card,
.results-card,
.error-card,
.stats-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin-bottom: 1.5rem;
}

.section-title,
.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.section-description {
  color: #6b7280;
  margin-bottom: 1.5rem;
}

/* Search Input */
.search-input-container {
  margin-bottom: 1.5rem;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2563eb;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6b7280;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #4b5563;
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: #3b82f6;
  border: 2px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-link {
  background: #eff6ff;
  color: #3b82f6;
  padding: 8px 16px;
  font-size: 12px;
}

.btn-link:hover {
  background: #dbeafe;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.search-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* City Info */
.city-details {
  display: grid;
  gap: 0.75rem;
}

.city-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.city-region,
.city-coordinates,
.bounding-box {
  color: #6b7280;
  font-family: monospace;
}

/* Mesh Info */
.mesh-details {
  display: grid;
  gap: 0.75rem;
}

.mesh-stat {
  color: #374151;
  font-weight: 500;
}

/* Results */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.results-info {
  flex: 1;
}

.search-info {
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.5rem;
}

.search-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  font-size: 0.75rem;
  color: #9ca3af;
}

.results-actions {
  display: flex;
  gap: 0.5rem;
}

/* Error Messages */
.error-card {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.error-title {
  color: #dc2626;
  margin-bottom: 1rem;
}

.error-message {
  color: #dc2626;
  margin-bottom: 0.5rem;
}

/* Statistics */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.stat-label {
  color: #6b7280;
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-title {
    font-size: 2rem;
  }

  .search-card,
  .info-card,
  .results-card,
  .error-card,
  .stats-card {
    padding: 1.5rem;
  }

  .results-header {
    flex-direction: column;
    align-items: stretch;
  }

  .building-item {
    flex-direction: column;
    gap: 1rem;
  }

  .building-actions {
    margin-left: 0;
    align-self: flex-start;
  }

  .search-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }

  .app-title {
    font-size: 1.75rem;
  }

  .app-subtitle {
    font-size: 1rem;
  }

  .search-card,
  .info-card,
  .results-card,
  .error-card,
  .stats-card {
    padding: 1rem;
  }
}
</style>
