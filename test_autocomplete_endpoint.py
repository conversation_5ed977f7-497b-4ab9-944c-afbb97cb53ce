#!/usr/bin/env python3
"""
Test script for the Mapy.cz autocomplete endpoint.
"""

import asyncio
import httpx
import sys
import os

# Add backend directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

async def test_autocomplete_endpoint():
    """Test the autocomplete endpoint directly."""
    print("🧪 Testing Mapy.cz Autocomplete Endpoint")
    print("=" * 50)
    
    # Import the endpoint function
    from reality_app import mapy_autocomplete
    
    test_queries = ["<PERSON>raha", "Brno", "Ost", "Plz"]
    
    for query in test_queries:
        print(f"🔍 Testing query: '{query}'")
        try:
            # Call the endpoint function directly
            results = await mapy_autocomplete(query)
            
            print(f"   ✅ Found {len(results)} suggestions")
            
            for i, suggestion in enumerate(results[:3]):  # Show first 3 results
                print(f"   {i+1}. {suggestion.name} ({suggestion.type})")
                print(f"      📍 {suggestion.lat:.4f}, {suggestion.lon:.4f}")
                print(f"      📦 bbox: {suggestion.bbox}")
            
            if len(results) > 3:
                print(f"   ... and {len(results) - 3} more")
            print()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            print()

async def test_with_http_client():
    """Test the endpoint via HTTP (if server is running)."""
    print("🌐 Testing via HTTP Client")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        async with httpx.AsyncClient() as client:
            # Test health endpoint first
            health_response = await client.get(f"{base_url}/health", timeout=5.0)
            if health_response.status_code == 200:
                print("✅ Server is running")
                
                # Test autocomplete endpoint
                test_queries = ["Praha", "Brno"]
                
                for query in test_queries:
                    print(f"🔍 Testing HTTP query: '{query}'")
                    try:
                        response = await client.get(
                            f"{base_url}/api/mapy-autocomplete",
                            params={"query": query},
                            timeout=10.0
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            print(f"   ✅ HTTP: Found {len(data)} suggestions")
                            
                            for i, item in enumerate(data[:2]):  # Show first 2 results
                                print(f"   {i+1}. {item['name']} ({item['type']})")
                                print(f"      📍 {item['lat']:.4f}, {item['lon']:.4f}")
                        else:
                            print(f"   ❌ HTTP Error: {response.status_code} - {response.text}")
                        print()
                        
                    except Exception as e:
                        print(f"   ❌ HTTP Request Error: {e}")
                        print()
            else:
                print("❌ Server is not running or not responding")
                
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")

async def main():
    """Main test function."""
    await test_autocomplete_endpoint()
    print("\n" + "=" * 50 + "\n")
    await test_with_http_client()

if __name__ == "__main__":
    asyncio.run(main())
