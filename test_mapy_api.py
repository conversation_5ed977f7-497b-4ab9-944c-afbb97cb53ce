#!/usr/bin/env python3
"""
Test script for the Mapy.cz API integration.
"""

import asyncio
import httpx
import sys
import os

# Add backend directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from reality_app import search_mapy_cz, MAPY_CZ_API_KEY

async def test_mapy_api():
    """Test the Mapy.cz API integration directly."""
    print("🧪 Testing Mapy.cz API Integration")
    print(f"API Key configured: {'Yes' if MAPY_CZ_API_KEY else 'No'}")
    print(f"API Key (first 10 chars): {MAPY_CZ_API_KEY[:10]}...")
    print()
    
    # Test queries
    test_queries = ["Praha", "Brno", "Ostrava", "Plzeň"]
    
    for query in test_queries:
        print(f"🔍 Testing query: '{query}'")
        try:
            results = await search_mapy_cz(query)
            print(f"   ✅ Found {len(results)} results")
            
            if results:
                first_result = results[0]
                print(f"   📍 First result: {first_result.get('name', 'N/A')}")
                position = first_result.get('position', {})
                if position:
                    print(f"   🌍 Coordinates: {position.get('lat', 'N/A')}, {position.get('lon', 'N/A')}")
                bbox = first_result.get('bbox', [])
                if bbox:
                    print(f"   📦 Bounding box: {bbox}")
            print()
            
        except Exception as e:
            print(f"   ❌ Error: {e}")
            print()

async def test_direct_api_call():
    """Test direct API call to Mapy.cz."""
    print("🌐 Testing direct Mapy.cz API call")
    
    try:
        async with httpx.AsyncClient() as client:
            url = "https://api.mapy.cz/suggest"
            params = {
                "query": "Praha",
                "type": "municipality,address",
                "apikey": MAPY_CZ_API_KEY,
                "lang": "en"
            }
            
            headers = {
                "User-Agent": "Reality2.0/1.0 (Czech Real Estate Search)",
                "Accept": "application/json",
                "Referer": "https://reality2.app"
            }
            
            print(f"URL: {url}")
            print(f"Params: {params}")
            print()
            
            response = await client.get(url, params=params, headers=headers, timeout=10.0)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            print()
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response data keys: {list(data.keys())}")
                items = data.get("items", [])
                print(f"Number of items: {len(items)}")
                
                if items:
                    print("First item:")
                    first_item = items[0]
                    for key, value in first_item.items():
                        print(f"  {key}: {value}")
            else:
                print(f"Error response: {response.text}")
                
    except Exception as e:
        print(f"❌ Direct API call failed: {e}")

async def main():
    """Main test function."""
    print("🏠 Reality 2.0 - Mapy.cz API Test")
    print("=" * 50)
    print()
    
    await test_direct_api_call()
    print("\n" + "=" * 50 + "\n")
    await test_mapy_api()

if __name__ == "__main__":
    asyncio.run(main())
